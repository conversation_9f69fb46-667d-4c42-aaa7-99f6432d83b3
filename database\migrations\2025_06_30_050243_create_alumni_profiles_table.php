<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('alumni_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->year('graduation_year');
            $table->string('current_occupation')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('location')->nullable();
            $table->string('field_of_work')->nullable();
            $table->text('bio')->nullable();
            $table->string('social_media_link')->nullable();
            $table->string('business_website')->nullable();
            $table->string('profile_photo')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('alumni_profiles');
    }
};

<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Auth\AdminAuthController;
use App\Http\Controllers\Auth\AlumniAuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// Admin Authentication Routes
Route::prefix('auth/admin')->group(function () {
    Route::get('login', [AdminAuthController::class, 'showLoginForm'])->name('admin.login');
    Route::post('login', [AdminAuthController::class, 'login']);
    Route::post('logout', [AdminAuthController::class, 'logout'])->name('admin.logout');
});

// Alumni Authentication Routes
Route::prefix('auth/alumni')->group(function () {
    Route::get('login', [AlumniAuthController::class, 'showLoginForm'])->name('alumni.login');
    Route::post('login', [AlumniAuthController::class, 'login']);
    Route::get('register', [AlumniAuthController::class, 'showRegistrationForm'])->name('alumni.register');
    Route::post('register', [AlumniAuthController::class, 'register']);
    Route::post('logout', [AlumniAuthController::class, 'logout'])->name('alumni.logout');
});

// Admin Dashboard Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->group(function () {
    Route::get('dashboard', function () {
        return view('admin.dashboard');
    })->name('admin.dashboard');
});

// Alumni Dashboard Routes
Route::middleware(['auth', 'alumni'])->prefix('alumni')->group(function () {
    Route::get('dashboard', function () {
        return view('alumni.dashboard');
    })->name('alumni.dashboard');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MotivationalQuote extends Model
{
    use HasFactory;

    protected $fillable = [
        'quote',
        'author',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public static function getRandomActiveQuote()
    {
        return self::where('is_active', true)->inRandomOrder()->first();
    }
}

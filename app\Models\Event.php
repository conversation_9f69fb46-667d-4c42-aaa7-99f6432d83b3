<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'event_date',
        'location',
        'poster_image',
        'max_participants',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'event_date' => 'datetime',
        'is_active' => 'boolean',
    ];

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function rsvps()
    {
        return $this->hasMany(EventRsvp::class);
    }

    public function attendees()
    {
        return $this->belongsToMany(User::class, 'event_rsvps')
                    ->withPivot('status', 'notes')
                    ->withTimestamps();
    }

    public function getAttendingCountAttribute()
    {
        return $this->rsvps()->where('status', 'attending')->count();
    }

    public function isUserAttending($userId)
    {
        return $this->rsvps()
                    ->where('user_id', $userId)
                    ->where('status', 'attending')
                    ->exists();
    }
}

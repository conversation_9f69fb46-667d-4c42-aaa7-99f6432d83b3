<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ForumCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function posts()
    {
        return $this->hasMany(ForumPost::class, 'category_id');
    }

    public function getPostsCountAttribute()
    {
        return $this->posts()->count();
    }

    public function getLatestPostAttribute()
    {
        return $this->posts()->latest()->first();
    }
}
